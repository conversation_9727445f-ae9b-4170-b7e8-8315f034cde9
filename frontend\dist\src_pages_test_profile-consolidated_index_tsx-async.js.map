{"version": 3, "sources": ["src/pages/test/profile-consolidated/TeamListCard.tsx", "src/pages/test/profile-consolidated/TodoManagement.tsx", "src/pages/test/profile-consolidated/UserProfileCard.tsx", "src/pages/test/profile-consolidated/index.tsx", "src/services/todo.ts"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Card,\r\n  Typography,\r\n  Tooltip,\r\n  List,\r\n  Flex,\r\n  Spin,\r\n  Alert,\r\n  message\r\n} from \"antd\";\r\nimport { TeamService } from \"@/services/team\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { TeamDetailResponse } from \"@/types/api\";\r\nimport {\r\n  CarOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n  ClockCircleOutlined,\r\n  CrownOutlined,\r\n  RightOutlined,\r\n  ExclamationCircleOutlined\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text, Title } = Typography;\r\n\r\n// 紧凑型布局样式\r\nconst styles = `\r\n  .team-item .ant-card-body {\r\n    padding: 0 !important;\r\n  }\r\n\r\n  .team-item:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .team-item {\r\n      margin-bottom: 8px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 576px) {\r\n    .team-stats-flex {\r\n      flex-wrap: wrap;\r\n      gap: 4px !important;\r\n    }\r\n  }\r\n`;\r\n\r\nconst TeamListCard: React.FC = () => {\r\n  // 团队列表状态管理\r\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\r\n\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const currentTeam = initialState?.currentTeam;\r\n\r\n  // 获取团队列表数据\r\n  useEffect(() => {\r\n    const fetchTeams = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const teamsData = await TeamService.getUserTeamsWithStats();\r\n        setTeams(teamsData);\r\n      } catch (error) {\r\n        console.error('获取团队列表失败:', error);\r\n        setError('获取团队列表失败');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTeams();\r\n  }, []);\r\n\r\n  // 团队切换处理函数\r\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\r\n    if (teamId === currentTeam?.id) {\r\n      message.info('您已经在当前团队中');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setSwitchingTeamId(teamId);\r\n      const response = await AuthService.selectTeam({ teamId });\r\n\r\n      // 检查后端返回的团队选择成功标识\r\n      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {\r\n        message.success(`已切换到团队：${teamName}`);\r\n\r\n        // 同步更新 initialState，等待更新完成后再跳转\r\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\r\n          try {\r\n            const [currentUser, currentTeam] = await Promise.all([\r\n              initialState.fetchUserInfo(),\r\n              initialState.fetchTeamInfo()\r\n            ]);\r\n\r\n            // 确保团队信息已正确获取\r\n            if (currentTeam && currentTeam.id === teamId) {\r\n              await setInitialState({\r\n                ...initialState,\r\n                currentUser,\r\n                currentTeam,\r\n              });\r\n\r\n              // 等待 initialState 更新完成后再跳转到仪表盘\r\n              setTimeout(() => {\r\n                history.push('/dashboard');\r\n              }, 100);\r\n            } else {\r\n              console.error('获取的团队信息与选择的团队不匹配');\r\n              message.error('团队切换失败，请重试');\r\n            }\r\n          } catch (error) {\r\n            console.error('更新 initialState 失败:', error);\r\n            message.error('团队切换失败，请重试');\r\n          }\r\n        } else {\r\n          // 如果没有 initialState 相关方法，直接跳转\r\n          history.push('/dashboard');\r\n        }\r\n      } else {\r\n        console.error('团队切换响应异常，未返回正确的团队信息');\r\n        message.error('团队切换失败，请重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('团队切换失败:', error);\r\n      message.error('团队切换失败');\r\n    } finally {\r\n      setSwitchingTeamId(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* 注入样式 */}\r\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\r\n\r\n      <Card\r\n        className=\"dashboard-card\"\r\n        style={{\r\n          borderRadius: 16,\r\n          boxShadow: \"0 6px 20px rgba(0,0,0,0.08)\",\r\n          border: \"none\",\r\n          background: \"linear-gradient(145deg, #ffffff, #f8faff)\",\r\n        }}\r\n        title={\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Title level={4} style={{\r\n              margin: 0,\r\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\r\n              WebkitBackgroundClip: 'text',\r\n              WebkitTextFillColor: 'transparent',\r\n              fontWeight: 600\r\n            }}>\r\n              团队列表\r\n            </Title>\r\n          </Flex>\r\n        }\r\n      >\r\n      {error ? (\r\n        <Alert\r\n          message=\"团队列表加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={teams}\r\n            renderItem={(item) => (\r\n              <List.Item>\r\n                <Card\r\n                  className=\"team-item\"\r\n                  style={{\r\n                    background: currentTeam?.id === item.id\r\n                      ? \"linear-gradient(135deg, #f0f9ff, #e6f4ff)\"\r\n                      : \"#fff\",\r\n                    borderRadius: 8,\r\n                    boxShadow: currentTeam?.id === item.id\r\n                      ? \"0 2px 8px rgba(24, 144, 255, 0.12)\"\r\n                      : \"0 1px 4px rgba(0,0,0,0.06)\",\r\n                    width: \"100%\",\r\n                    borderLeft: `3px solid ${item.isCreator ? \"#722ed1\" : \"#52c41a\"}`,\r\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                    border: currentTeam?.id === item.id\r\n                      ? \"1px solid #91caff\"\r\n                      : \"1px solid #f0f0f0\",\r\n                    padding: \"12px 16px\",\r\n                    position: 'relative',\r\n                    overflow: 'hidden'\r\n                  }}\r\n                  hoverable\r\n                  onMouseEnter={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(-2px)';\r\n                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(0)';\r\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';\r\n                    }\r\n                  }}\r\n                >\r\n\r\n                  {/* 紧凑型布局 */}\r\n                  <Flex justify=\"space-between\" align=\"center\" style={{ width: '100%' }}>\r\n                    {/* 左侧：团队信息 */}\r\n                    <Flex vertical gap={6} style={{ flex: 1 }}>\r\n                      {/* 团队名称行 */}\r\n                      <Flex align=\"center\" gap={8}>\r\n                        <div\r\n                          style={{\r\n                            cursor: currentTeam?.id === item.id ? 'default' : 'pointer',\r\n                            padding: '2px 4px',\r\n                            borderRadius: 4,\r\n                            transition: 'all 0.2s ease'\r\n                          }}\r\n                          onClick={() => handleTeamSwitch(item.id, item.name)}\r\n                          onMouseEnter={(e) => {\r\n                            if (currentTeam?.id !== item.id) {\r\n                              e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';\r\n                            }\r\n                          }}\r\n                          onMouseLeave={(e) => {\r\n                            if (currentTeam?.id !== item.id) {\r\n                              e.currentTarget.style.background = 'transparent';\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Text\r\n                            strong\r\n                            style={{\r\n                              fontSize: 16,\r\n                              color: currentTeam?.id === item.id ? '#1890ff' : '#262626',\r\n                              lineHeight: 1.2\r\n                            }}\r\n                          >\r\n                            {item.name}\r\n                          </Text>\r\n                          {currentTeam?.id !== item.id && (\r\n                            <RightOutlined\r\n                              style={{\r\n                                fontSize: 10,\r\n                                color: '#8c8c8c',\r\n                                marginLeft: 6,\r\n                                verticalAlign: 'middle',\r\n                                display: 'inline-flex',\r\n                                alignItems: 'center'\r\n                              }}\r\n                            />\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* 状态标识 */}\r\n                        {currentTeam?.id === item.id && (\r\n                          <span style={{\r\n                            background: '#1890ff',\r\n                            color: 'white',\r\n                            padding: '1px 6px',\r\n                            borderRadius: 8,\r\n                            fontSize: 10,\r\n                            fontWeight: 500\r\n                          }}>\r\n                            当前\r\n                          </span>\r\n                        )}\r\n\r\n                        {switchingTeamId === item.id && (\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <Spin size=\"small\" />\r\n                            <Text style={{ fontSize: 10, color: '#666' }}>切换中</Text>\r\n                          </Flex>\r\n                        )}\r\n                      </Flex>\r\n\r\n                      {/* 团队基本信息 */}\r\n                      <Flex align=\"center\" gap={16}>\r\n                        <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <ClockCircleOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                            <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                              {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                            </Text>\r\n                          </Flex>\r\n                        </Tooltip>\r\n\r\n                        <Tooltip title={`团队成员: ${item.memberCount}人`}>\r\n                          <Flex align=\"center\" gap={4}>\r\n                            <TeamOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                            <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                              {item.memberCount} 人\r\n                            </Text>\r\n                          </Flex>\r\n                        </Tooltip>\r\n\r\n                        {/* 角色标识 - 移动到人员数量后面 */}\r\n                        <span style={{\r\n                          background: item.isCreator ? '#722ed1' : '#52c41a',\r\n                          color: 'white',\r\n                          padding: '2px 6px',\r\n                          borderRadius: 8,\r\n                          fontSize: 10,\r\n                          fontWeight: 500,\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: 2\r\n                        }}>\r\n                          {item.isCreator ? (\r\n                            <>\r\n                              <CrownOutlined style={{ fontSize: 9 }} />\r\n                              管理员\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <UserOutlined style={{ fontSize: 9 }} />\r\n                              成员\r\n                            </>\r\n                          )}\r\n                        </span>\r\n                      </Flex>\r\n                    </Flex>\r\n\r\n                    {/* 右侧：紧凑型指标卡片 */}\r\n                    <Flex gap={8} align=\"center\">\r\n                      {/* 车辆资源 */}\r\n                      <div style={{\r\n                        background: '#f0f7ff',\r\n                        border: '1px solid #d9e8ff',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <CarOutlined style={{ color: \"#1890ff\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#1890ff', lineHeight: 1 }}>\r\n                            {item.stats?.vehicles || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>车辆</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 人员资源 */}\r\n                      <div style={{\r\n                        background: '#f6ffed',\r\n                        border: '1px solid #d1f0be',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <UserOutlined style={{ color: \"#52c41a\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#52c41a', lineHeight: 1 }}>\r\n                            {item.stats?.personnel || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>人员</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 临期事项 */}\r\n                      <div style={{\r\n                        background: '#fff7e6',\r\n                        border: '1px solid #ffd666',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <ExclamationCircleOutlined style={{ color: \"#faad14\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#faad14', lineHeight: 1 }}>\r\n                            {item.stats?.expiring || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>临期</Text>\r\n                        </Flex>\r\n                      </div>\r\n\r\n                      {/* 逾期事项 */}\r\n                      <div style={{\r\n                        background: '#fff1f0',\r\n                        border: '1px solid #ffccc7',\r\n                        borderRadius: 6,\r\n                        padding: '6px 8px',\r\n                        textAlign: 'center',\r\n                        minWidth: '50px'\r\n                      }}>\r\n                        <Flex vertical align=\"center\" gap={2}>\r\n                          <ExclamationCircleOutlined style={{ color: \"#ff4d4f\", fontSize: 14 }} />\r\n                          <Text strong style={{ fontSize: 16, color: '#ff4d4f', lineHeight: 1 }}>\r\n                            {item.stats?.overdue || 0}\r\n                          </Text>\r\n                          <Text style={{ fontSize: 9, color: '#666' }}>逾期</Text>\r\n                        </Flex>\r\n                      </div>\r\n                    </Flex>\r\n                  </Flex>\r\n                </Card>\r\n              </List.Item>\r\n            )}\r\n          />\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TeamListCard;", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  Card,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  List,\r\n  Modal,\r\n  Progress,\r\n  Select,\r\n  Space,\r\n  Tabs,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  message,\r\n} from \"antd\";\r\nimport { TodoService } from \"@/services/todo\";\r\nimport type { TodoResponse, TodoStatsResponse } from \"@/types/api\";\r\nimport {\r\n  CheckOutlined,\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  MoreOutlined,\r\n  PlusOutlined,\r\n  SearchOutlined,\r\n  CalendarOutlined,\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text } = Typography;\r\nconst { TabPane } = Tabs;\r\n\r\n// 使用API类型定义，不需要重复定义接口\r\ninterface TodoManagementProps {\r\n  onAddTodo?: (todo: TodoResponse) => void;\r\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\r\n  onDeleteTodo?: (id: number) => void;\r\n}\r\n\r\nconst TodoManagement: React.FC<TodoManagementProps> = (props) => {\r\n  // TODO数据状态管理\r\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\r\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\r\n    highPriorityCount: 0,\r\n    mediumPriorityCount: 0,\r\n    lowPriorityCount: 0,\r\n    totalCount: 0,\r\n    completedCount: 0,\r\n    completionPercentage: 0,\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 待办事项状态管理\r\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\r\n  const [todoForm] = Form.useForm();\r\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\r\n\r\n  // 过滤器状态\r\n  const [activeTab, setActiveTab] = useState<\"all\" | \"pending\" | \"completed\">(\r\n    \"pending\"\r\n  );\r\n  const [searchText, setSearchText] = useState(\"\");\r\n\r\n  // 获取TODO数据\r\n  useEffect(() => {\r\n    const fetchTodoData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        console.log('TodoManagement: 开始获取TODO数据');\r\n\r\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\r\n        const todosPromise = TodoService.getUserTodos().catch(error => {\r\n          console.error('获取TODO列表失败:', error);\r\n          return [];\r\n        });\r\n\r\n        const statsPromise = TodoService.getTodoStats().catch(error => {\r\n          console.error('获取TODO统计失败:', error);\r\n          return {\r\n            highPriorityCount: 0,\r\n            mediumPriorityCount: 0,\r\n            lowPriorityCount: 0,\r\n            totalCount: 0,\r\n            completedCount: 0,\r\n            completionPercentage: 0,\r\n          };\r\n        });\r\n\r\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\r\n\r\n        console.log('TodoManagement: 获取到TODO列表:', todos);\r\n        console.log('TodoManagement: 获取到统计数据:', stats);\r\n\r\n        setPersonalTasks(todos);\r\n        setTodoStats(stats);\r\n      } catch (error) {\r\n        console.error('获取TODO数据时发生未知错误:', error);\r\n        setError('获取TODO数据失败，请刷新页面重试');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTodoData();\r\n  }, []);\r\n\r\n  // 根据激活的标签和搜索文本过滤任务\r\n  const filteredPersonalTasks = personalTasks.filter((task) => {\r\n    // 根据标签过滤\r\n    if (activeTab === \"pending\" && task.status === 1) return false;\r\n    if (activeTab === \"completed\" && task.status === 0) return false;\r\n\r\n    // 根据搜索文本过滤\r\n    if (\r\n      searchText &&\r\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n\r\n  // 处理待办事项操作\r\n  const handleToggleTodoStatus = async (id: number) => {\r\n    try {\r\n      const task = personalTasks.find(t => t.id === id);\r\n      if (!task) {\r\n        message.error('任务不存在');\r\n        return;\r\n      }\r\n\r\n      const newStatus = task.status === 0 ? 1 : 0;\r\n      console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);\r\n\r\n      await TodoService.updateTodo(id, { status: newStatus });\r\n\r\n      // 更新本地状态\r\n      setPersonalTasks(\r\n        personalTasks.map((task) =>\r\n          task.id === id ? { ...task, status: newStatus } : task\r\n        )\r\n      );\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');\r\n    } catch (error) {\r\n      console.error('更新任务状态失败:', error);\r\n      message.error('更新任务状态失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  const handleAddOrUpdateTodo = async (values: any) => {\r\n    try {\r\n      console.log('TodoManagement: 保存任务', { editingTodoId, values });\r\n\r\n      if (editingTodoId) {\r\n        // 更新现有待办事项\r\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks(\r\n          personalTasks.map((task) =>\r\n            task.id === editingTodoId ? updatedTodo : task\r\n          )\r\n        );\r\n        message.success('任务更新成功');\r\n      } else {\r\n        // 添加新待办事项\r\n        const newTodo = await TodoService.createTodo({\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks([newTodo, ...personalTasks]);\r\n        message.success('任务创建成功');\r\n      }\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      // 重置表单并关闭模态框\r\n      setTodoModalVisible(false);\r\n      setEditingTodoId(null);\r\n      todoForm.resetFields();\r\n    } catch (error) {\r\n      console.error('保存任务失败:', error);\r\n      const action = editingTodoId ? '更新' : '创建';\r\n      message.error(`${action}任务失败，请检查网络连接后重试`);\r\n    }\r\n  };\r\n\r\n  const handleDeleteTodo = async (id: number) => {\r\n    try {\r\n      console.log('TodoManagement: 删除任务', id);\r\n\r\n      await TodoService.deleteTodo(id);\r\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      message.success('任务删除成功');\r\n    } catch (error) {\r\n      console.error('删除任务失败:', error);\r\n      message.error('删除任务失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className=\"dashboard-card\"\r\n      style={{\r\n        borderRadius: 12,\r\n        boxShadow: \"0 4px 12px rgba(0,0,0,0.05)\",\r\n        border: \"none\",\r\n        background: \"linear-gradient(145deg, #ffffff, #f5f8ff)\",\r\n      }}\r\n      title={\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Text strong>待办事项</Text>\r\n        </Flex>\r\n      }\r\n    >\r\n      {/* 标题行：搜索框、新增按钮、优先级计数和完成率 */}\r\n      <Flex\r\n        justify=\"space-between\"\r\n        align=\"center\"\r\n        style={{\r\n          marginBottom: 16,\r\n          gap: 12,\r\n          flexWrap: \"wrap\",\r\n          padding: \"12px 16px\",\r\n          background: \"#fafbfc\",\r\n          borderRadius: 8,\r\n          border: \"1px solid #f0f0f0\"\r\n        }}\r\n      >\r\n        {/* 左侧：搜索框和新增按钮 */}\r\n        <Flex align=\"center\" gap={12} style={{ flex: 1, minWidth: 280 }}>\r\n          <Input.Search\r\n            placeholder=\"搜索任务...\"\r\n            allowClear\r\n            prefix={<SearchOutlined />}\r\n            value={searchText}\r\n            onChange={(e) => setSearchText(e.target.value)}\r\n            style={{\r\n              flex: 1,\r\n              maxWidth: 300\r\n            }}\r\n            size=\"middle\"\r\n          />\r\n\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => {\r\n              setEditingTodoId(null);\r\n              todoForm.resetFields();\r\n              setTodoModalVisible(true);\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n              fontWeight: 500,\r\n              minWidth: 80\r\n            }}\r\n            size=\"middle\"\r\n          >\r\n            新增\r\n          </Button>\r\n        </Flex>\r\n\r\n        {/* 右侧：优先级计数和完成率 */}\r\n        <Flex align=\"center\" gap={16} style={{ flexShrink: 0 }}>\r\n          {/* 优先级计数指示器 */}\r\n          <Space size={12}>\r\n            <Tooltip title={`高优先级任务: ${todoStats.highPriorityCount}个`}>\r\n              <Flex align=\"center\" gap={6}>\r\n                <div\r\n                  style={{\r\n                    width: 8,\r\n                    height: 8,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#ff4d4f\",\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500, color: \"#262626\" }}>\r\n                  高: {todoStats.highPriorityCount}\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}>\r\n              <Flex align=\"center\" gap={6}>\r\n                <div\r\n                  style={{\r\n                    width: 8,\r\n                    height: 8,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#faad14\",\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500, color: \"#262626\" }}>\r\n                  中: {todoStats.mediumPriorityCount}\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n\r\n            <Tooltip title={`低优先级任务: ${todoStats.lowPriorityCount}个`}>\r\n              <Flex align=\"center\" gap={6}>\r\n                <div\r\n                  style={{\r\n                    width: 8,\r\n                    height: 8,\r\n                    borderRadius: \"50%\",\r\n                    background: \"#52c41a\",\r\n                  }}\r\n                />\r\n                <Text style={{ fontSize: 13, fontWeight: 500, color: \"#262626\" }}>\r\n                  低: {todoStats.lowPriorityCount}\r\n                </Text>\r\n              </Flex>\r\n            </Tooltip>\r\n          </Space>\r\n\r\n          <Divider type=\"vertical\" style={{ height: 20, backgroundColor: \"#d9d9d9\" }} />\r\n\r\n          {/* 完成率显示 */}\r\n          <Tooltip title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}>\r\n            <Flex align=\"center\" gap={8}>\r\n              <Text style={{ fontSize: 13, fontWeight: 500, color: \"#595959\" }}>\r\n                完成率:\r\n              </Text>\r\n              <Progress\r\n                percent={todoStats.completionPercentage}\r\n                size=\"small\"\r\n                style={{ width: 100 }}\r\n                strokeColor=\"#52c41a\"\r\n                showInfo={false}\r\n              />\r\n              <Text style={{ fontSize: 13, fontWeight: 600, color: \"#262626\" }}>\r\n                {todoStats.completionPercentage}%\r\n              </Text>\r\n            </Flex>\r\n          </Tooltip>\r\n        </Flex>\r\n      </Flex>\r\n\r\n      {/* 第二行：标签页 */}\r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onChange={(key) =>\r\n          setActiveTab(key as \"all\" | \"pending\" | \"completed\")\r\n        }\r\n        size=\"middle\"\r\n        style={{ marginBottom: 8 }}\r\n      >\r\n        <TabPane tab=\"全部\" key=\"all\" />\r\n        <TabPane tab=\"待处理\" key=\"pending\" />\r\n        <TabPane tab=\"已完成\" key=\"completed\" />\r\n      </Tabs>\r\n\r\n      {/* 待办事项列表 */}\r\n      {error ? (\r\n        <Alert\r\n          message=\"TODO数据加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={filteredPersonalTasks}\r\n            renderItem={(item) => {\r\n          return (\r\n            <List.Item\r\n              className=\"todo-item\"\r\n              style={{\r\n                padding: \"10px 16px\",\r\n                marginBottom: 12,\r\n                borderRadius: 8,\r\n                background: \"#fff\",\r\n                opacity: item.status === 1 ? 0.7 : 1,\r\n                borderLeft: `3px solid ${\r\n                  item.status === 1\r\n                    ? \"#52c41a\"\r\n                    : item.priority === 3\r\n                    ? \"#ff4d4f\"\r\n                    : item.priority === 2\r\n                    ? \"#faad14\"\r\n                    : \"#8c8c8c\"\r\n                }`,\r\n                boxShadow: \"0 1px 4px rgba(0,0,0,0.05)\",\r\n              }}\r\n            >\r\n              <Flex align=\"center\" gap={12} style={{ width: \"100%\" }}>\r\n                {/* 左侧状态和优先级指示器 */}\r\n                <Flex vertical align=\"center\">\r\n                  {item.status === 1 ? (\r\n                    <Flex\r\n                      align=\"center\"\r\n                      justify=\"center\"\r\n                      style={{\r\n                        width: 22,\r\n                        height: 22,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#52c41a\",\r\n                      }}\r\n                    >\r\n                      <CheckOutlined\r\n                        style={{ color: \"#fff\", fontSize: 12 }}\r\n                      />\r\n                    </Flex>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 18,\r\n                        height: 18,\r\n                        borderRadius: \"50%\",\r\n                        border: `2px solid ${\r\n                          item.priority === 3\r\n                            ? \"#ff4d4f\"\r\n                            : item.priority === 2\r\n                            ? \"#faad14\"\r\n                            : \"#8c8c8c\"\r\n                        }`,\r\n                      }}\r\n                    />\r\n                  )}\r\n\r\n                  <div\r\n                    style={{\r\n                      width: 2,\r\n                      height: 24,\r\n                      background: \"#f0f0f0\",\r\n                      marginTop: 4,\r\n                    }}\r\n                  />\r\n                </Flex>\r\n\r\n                {/* 任务信息区 */}\r\n                <Flex vertical style={{ flex: 1 }}>\r\n                  <Text\r\n                    style={{\r\n                      fontSize: 14,\r\n                      fontWeight:\r\n                        item.priority === 3 ? 500 : \"normal\",\r\n                      textDecoration: item.status === 1\r\n                        ? \"line-through\"\r\n                        : \"none\",\r\n                      color: item.status === 1 ? \"#8c8c8c\" : \"#262626\",\r\n                    }}\r\n                  >\r\n                    {item.title}\r\n                  </Text>\r\n\r\n                  {/* 显示创建日期 */}\r\n                  <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\r\n                    <CalendarOutlined\r\n                      style={{\r\n                        fontSize: 12,\r\n                        color: \"#8c8c8c\",\r\n                      }}\r\n                    />\r\n                    <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                      创建于: {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                    </Text>\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 操作按钮区 */}\r\n                <Dropdown\r\n                  trigger={['click']}\r\n                  menu={{\r\n                    items: [\r\n                      {\r\n                        key: 'complete',\r\n                        label: item.status === 1 ? '标记未完成' : '标记完成',\r\n                        icon: (\r\n                          <CheckOutlined\r\n                            style={{\r\n                              color: item.status === 1 ? '#8c8c8c' : '#52c41a',\r\n                              fontSize: 14 \r\n                            }} \r\n                          />\r\n                        )\r\n                      },\r\n                      {\r\n                        key: 'edit',\r\n                        label: '编辑任务',\r\n                        icon: <EditOutlined style={{ color: '#8c8c8c' }} />\r\n                      },\r\n                      {\r\n                        key: 'delete',\r\n                        label: '删除任务',\r\n                        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\r\n                        danger: true\r\n                      }\r\n                    ],\r\n                    onClick: ({ key }) => {\r\n                      if (key === \"complete\") {\r\n                        handleToggleTodoStatus(item.id);\r\n                      } else if (key === \"edit\") {\r\n                        setEditingTodoId(item.id);\r\n                        todoForm.setFieldsValue({\r\n                          name: item.title,\r\n                          priority: item.priority\r\n                        });\r\n                        setTodoModalVisible(true);\r\n                      } else if (key === \"delete\") {\r\n                        handleDeleteTodo(item.id);\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  <Button \r\n                    type=\"text\" \r\n                    size=\"small\" \r\n                    icon={<MoreOutlined />} \r\n                    style={{ width: 32, height: 32 }} \r\n                  />\r\n                </Dropdown>\r\n              </Flex>\r\n            </List.Item>\r\n          );\r\n        }}\r\n      />\r\n\r\n      {/* 待办事项表单模态框 */}\r\n      <Modal\r\n        title={editingTodoId ? \"编辑待办事项\" : \"新增待办事项\"}\r\n        open={todoModalVisible}\r\n        onCancel={() => {\r\n          setTodoModalVisible(false);\r\n          todoForm.resetFields();\r\n        }}\r\n        onOk={() => {\r\n          todoForm.submit();\r\n        }}\r\n        centered\r\n        destroyOnClose\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              todoForm.submit();\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n            }}\r\n          >\r\n            {editingTodoId ? \"更新任务\" : \"创建任务\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Form\r\n          form={todoForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleAddOrUpdateTodo}\r\n          autoComplete=\"off\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"任务名称\"\r\n            rules={[{ required: true, message: \"请输入任务名称\" }]}\r\n          >\r\n            <Input\r\n              placeholder=\"请输入任务名称\"\r\n              size=\"large\"\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"priority\"\r\n            label=\"优先级\"\r\n            initialValue={2}\r\n            rules={[{ required: true, message: \"请选择优先级\" }]}\r\n          >\r\n            <Select\r\n              size=\"large\"\r\n              options={[\r\n                { value: 3, label: \"高优先级\" },\r\n                { value: 2, label: \"中优先级\" },\r\n                { value: 1, label: \"低优先级\" },\r\n              ]}\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TodoManagement;", "import {\r\n  Bar<PERSON>hartOutlined,\r\n  CheckOutlined,\r\n  EditOutlined,\r\n  LogoutOutlined,\r\n  MailOutlined,\r\n  PhoneOutlined,\r\n  SettingOutlined,\r\n  TagOutlined,\r\n  UserOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Divider,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  Modal,\r\n  Row,\r\n  Space,\r\n  Steps,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  Avatar,\r\n} from \"antd\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { UserService } from \"@/services/user\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from \"@/types/api\";\r\n \r\nconst { Title, Text } = Typography;\r\nconst { Step } = Steps;\r\n\r\nconst UserProfileCard: React.FC = () => {\r\n  // 用户详细信息状态\r\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\r\n    name: \"\",\r\n    position: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    telephone: \"\",\r\n    registerDate: \"\",\r\n    lastLoginTime: \"\",\r\n    lastLoginTeam: \"\",\r\n    teamCount: 0,\r\n    avatar: \"\",\r\n  });\r\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\r\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\r\n\r\n  // 个人统计数据状态\r\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\r\n    vehicles: 0,\r\n    personnel: 0,\r\n    warnings: 0,\r\n    alerts: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState<string | null>(null);\r\n\r\n  // 订阅计划数据\r\n  const subscriptionPlans = [\r\n    {\r\n      id: \"basic\",\r\n      name: \"基础版\",\r\n      price: 0,\r\n      description: \"适合小团队使用\",\r\n      features: [\"最多5个团队\", \"最多20辆车辆\", \"基础安全监控\", \"基本报告功能\"],\r\n    },\r\n    {\r\n      id: \"professional\",\r\n      name: \"专业版\",\r\n      price: 199,\r\n      description: \"适合中小型企业\",\r\n      features: [\r\n        \"最多20个团队\",\r\n        \"最多100辆车辆\",\r\n        \"高级安全监控\",\r\n        \"详细分析报告\",\r\n        \"设备状态预警\",\r\n        \"优先技术支持\",\r\n      ],\r\n    },\r\n    {\r\n      id: \"enterprise\",\r\n      name: \"企业版\",\r\n      price: 499,\r\n      description: \"适合大型企业\",\r\n      features: [\r\n        \"不限团队数量\",\r\n        \"不限车辆数量\",\r\n        \"AI安全分析\",\r\n        \"实时监控告警\",\r\n        \"定制化报告\",\r\n        \"专属客户经理\",\r\n        \"24/7技术支持\",\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // 当前订阅信息\r\n  const currentSubscription = {\r\n    planId: \"basic\",\r\n    expires: \"2025-12-31\",\r\n  };\r\n\r\n  // 状态管理\r\n  const [editProfileModalVisible, setEditProfileModalVisible] = useState(false);\r\n  const [subscriptionModalVisible, setSubscriptionModalVisible] =\r\n    useState(false);\r\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\r\n  const [logoutLoading, setLogoutLoading] = useState(false);\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [editProfileForm] = Form.useForm();\r\n\r\n  const { setInitialState } = useModel('@@initialState');\r\n\r\n  // 获取用户数据\r\n  useEffect(() => {\r\n    console.log('UserProfileCard: useEffect 开始执行');\r\n\r\n    const fetchUserData = async () => {\r\n      try {\r\n        console.log('UserProfileCard: 开始获取用户数据');\r\n\r\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\r\n        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {\r\n          console.error('获取用户详细信息失败:', error);\r\n          setUserInfoError('获取用户详细信息失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const statsPromise = UserService.getUserPersonalStats().catch(error => {\r\n          console.error('获取统计数据失败:', error);\r\n          setStatsError('获取统计数据失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);\r\n\r\n        if (userDetail) {\r\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\r\n          setUserInfo(userDetail);\r\n          setUserInfoError(null);\r\n        }\r\n\r\n        if (stats) {\r\n          console.log('UserProfileCard: 获取到统计数据:', stats);\r\n          setPersonalStats(stats);\r\n          setStatsError(null);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('获取用户数据时发生未知错误:', error);\r\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\r\n        setStatsError('获取统计数据失败，请刷新页面重试');\r\n      } finally {\r\n        setUserInfoLoading(false);\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, []);\r\n\r\n  // 退出登录处理函数\r\n  const handleLogout = async () => {\r\n    try {\r\n      setLogoutLoading(true);\r\n\r\n      // 调用退出登录API\r\n      await AuthService.logout();\r\n\r\n      // 清除 initialState\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n\r\n      // 跳转到登录页面\r\n      history.push('/user/login');\r\n\r\n    } catch (error) {\r\n      console.error('退出登录失败:', error);\r\n      // 即使API调用失败，也要清除本地状态并跳转\r\n      if (setInitialState) {\r\n        await setInitialState({\r\n          currentUser: undefined,\r\n          currentTeam: undefined,\r\n        });\r\n      }\r\n      history.push('/user/login');\r\n    } finally {\r\n      setLogoutLoading(false);\r\n      setLogoutModalVisible(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className=\"dashboard-card\" styles={{ body: { padding: 32 } }}\r\n      >\r\n\r\n\r\n        {/* 用户信息主卡片 */}\r\n        {userInfoError ? (\r\n          <Alert\r\n            message=\"用户信息加载失败\"\r\n            description={userInfoError}\r\n            type=\"error\"\r\n            showIcon\r\n            style={{ marginBottom: 24 }}\r\n          />\r\n        ) : (\r\n          <Spin spinning={userInfoLoading}>\r\n            {/* 使用 Card 组件替代自定义 div */}\r\n            <Card\r\n              style={{\r\n                background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n                borderRadius: 16,\r\n                color: \"white\",\r\n                position: \"relative\",\r\n                overflow: \"hidden\",\r\n                height: 140,\r\n                border: \"none\",\r\n              }}\r\n              styles={{ body: { padding: 24, height: \"100%\" } }}\r\n            >\r\n              {/* 操作按钮区域 - 使用 Space 组件 */}\r\n              <Space\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: 16,\r\n                  right: 16,\r\n                  zIndex: 20,\r\n                }}\r\n                size={8}\r\n              >\r\n                <Tooltip title=\"设置\">\r\n                  <Dropdown\r\n                    menu={{\r\n                      items: [\r\n                        {\r\n                          key: \"editProfile\",\r\n                          icon: <EditOutlined />,\r\n                          label: \"修改资料\",\r\n                          onClick: () => {\r\n                            setEditProfileModalVisible(true);\r\n                            setCurrentStep(0);\r\n                            editProfileForm.setFieldsValue({\r\n                              name: userInfo.name,\r\n                              email: userInfo.email,\r\n                              telephone: userInfo.phone || userInfo.telephone,\r\n                            });\r\n                          },\r\n                        },\r\n                        {\r\n                          key: \"subscription\",\r\n                          icon: <TagOutlined />,\r\n                          label: \"订阅套餐\",\r\n                          onClick: () => setSubscriptionModalVisible(true),\r\n                        },\r\n                      ],\r\n                    }}\r\n                    trigger={[\"click\"]}\r\n                    placement=\"bottomRight\"\r\n                  >\r\n                    <Button\r\n                      type=\"text\"\r\n                      shape=\"circle\"\r\n                      icon={<SettingOutlined />}\r\n                      style={{\r\n                        color: \"rgba(255,255,255,0.9)\",\r\n                        backgroundColor: \"rgba(255,255,255,0.15)\",\r\n                        border: \"none\",\r\n                        transition: \"all 0.2s\",\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.25)\";\r\n                        e.currentTarget.style.color = \"white\";\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.15)\";\r\n                        e.currentTarget.style.color = \"rgba(255,255,255,0.9)\";\r\n                      }}\r\n                    />\r\n                  </Dropdown>\r\n                </Tooltip>\r\n\r\n                <Tooltip title=\"退出登录\">\r\n                  <Button\r\n                    type=\"text\"\r\n                    shape=\"circle\"\r\n                    icon={<LogoutOutlined />}\r\n                    onClick={() => setLogoutModalVisible(true)}\r\n                    style={{\r\n                      color: \"rgba(255,255,255,0.9)\",\r\n                      backgroundColor: \"rgba(255,255,255,0.15)\",\r\n                      border: \"none\",\r\n                      transition: \"all 0.2s\",\r\n                    }}\r\n                    onMouseEnter={(e) => {\r\n                      e.currentTarget.style.backgroundColor = \"rgba(255,77,79,0.3)\";\r\n                      e.currentTarget.style.color = \"#ff4d4f\";\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.15)\";\r\n                      e.currentTarget.style.color = \"rgba(255,255,255,0.9)\";\r\n                    }}\r\n                  />\r\n                </Tooltip>\r\n              </Space>\r\n              {/* 装饰性背景元素 */}\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: -25,\r\n                  right: -25,\r\n                  width: 100,\r\n                  height: 100,\r\n                  background: \"rgba(255,255,255,0.1)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  bottom: -30,\r\n                  left: -30,\r\n                  width: 80,\r\n                  height: 80,\r\n                  background: \"rgba(255,255,255,0.05)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: \"50%\",\r\n                  right: \"20%\",\r\n                  width: 60,\r\n                  height: 60,\r\n                  background: \"rgba(255,255,255,0.03)\",\r\n                  borderRadius: \"50%\",\r\n                  transform: \"translateY(-50%)\",\r\n                }}\r\n              />\r\n\r\n              {/* 主要内容区域 - 使用 Flex 组件 */}\r\n              <Flex\r\n                align=\"center\"\r\n                gap={32}\r\n                style={{ position: \"relative\", zIndex: 1, width: \"100%\" }}\r\n              >\r\n                {/* 左侧：用户基本信息区域 */}\r\n                <Flex align=\"center\" style={{ flex: \"0 0 320px\", minWidth: 320 }}>\r\n                  {/* 用户头像 - 使用 Ant Design Avatar 组件 */}\r\n                  <Avatar\r\n                    size={64}\r\n                    shape=\"square\"\r\n                    style={{\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      marginRight: 20,\r\n                      fontSize: 24,\r\n                      fontWeight: 600,\r\n                      border: \"2px solid rgba(255,255,255,0.3)\",\r\n                    }}\r\n                  >\r\n                    {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : <UserOutlined />}\r\n                  </Avatar>\r\n\r\n                  {/* 用户信息 - 使用 Space 组件垂直布局 */}\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Title\r\n                      level={3}\r\n                      style={{\r\n                        margin: 0,\r\n                        color: \"white\",\r\n                        fontSize: 22,\r\n                        fontWeight: 600,\r\n                      }}\r\n                    >\r\n                      {userInfo.name || \"加载中...\"}\r\n                    </Title>\r\n\r\n                    {/* 联系信息 - 使用 Space 组件垂直排列 */}\r\n                    <Space direction=\"vertical\" size={4}>\r\n                      {userInfo.email && (\r\n                        <Space size={6} align=\"center\">\r\n                          <MailOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                          <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                            {userInfo.email}\r\n                          </Text>\r\n                        </Space>\r\n                      )}\r\n                      {(userInfo.phone || userInfo.telephone) && (\r\n                        <Space size={6} align=\"center\">\r\n                          <PhoneOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                          <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                            {userInfo.phone || userInfo.telephone}\r\n                          </Text>\r\n                        </Space>\r\n                      )}\r\n                    </Space>\r\n\r\n                    {/* 注册日期 */}\r\n                    {userInfo.registerDate && (\r\n                      <Text style={{ fontSize: 13, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                        注册于 {userInfo.registerDate}\r\n                      </Text>\r\n                    )}\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 分割线 */}\r\n                <Divider\r\n                  type=\"vertical\"\r\n                  style={{\r\n                    height: \"80px\",\r\n                    borderColor: \"rgba(255,255,255,0.3)\",\r\n                    margin: \"0 16px\",\r\n                  }}\r\n                />\r\n\r\n                {/* 中间：数据统计概览 - 使用 Flex 组件 */}\r\n                <Flex\r\n                  vertical\r\n                  style={{\r\n                    flex: \"1 1 auto\" \r\n                  }}\r\n                >\r\n                  {/* 标题区域 - 使用 Space 组件 */}\r\n                  <Space\r\n                    align=\"center\"\r\n                    style={{\r\n                      justifyContent: \"center\",\r\n                      marginBottom: 30,\r\n                      height: 20,\r\n                    }}\r\n                  >\r\n                    <BarChartOutlined\r\n                      style={{\r\n                        fontSize: 16,\r\n                        color: \"rgba(255,255,255,0.9)\",\r\n                      }}\r\n                    />\r\n                    <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 14, fontWeight: 600, lineHeight: 1 }}>\r\n                      数据概览\r\n                    </Text>\r\n                  </Space>\r\n\r\n                  {statsError ? (\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\" }}>\r\n                      数据加载失败\r\n                    </Text>\r\n                  ) : (\r\n                    <Spin spinning={statsLoading}>\r\n                      {/* 统计数据 - 使用 Flex 组件和自定义样式 */}\r\n                      <Flex justify=\"space-around\" align=\"center\">\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.vehicles}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            车辆\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.personnel}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            人员\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.warnings}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            预警\r\n                          </div>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <div style={{\r\n                            fontSize: 20,\r\n                            fontWeight: 700,\r\n                            color: \"white\",\r\n                            lineHeight: 1,\r\n                          }}>\r\n                            {personalStats.alerts}\r\n                          </div>\r\n                          <div style={{\r\n                            fontSize: 13,\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            marginTop: 3,\r\n                          }}>\r\n                            告警\r\n                          </div>\r\n                        </div>\r\n                      </Flex>\r\n                    </Spin>\r\n                  )}\r\n                </Flex>\r\n\r\n                {/* 分割线 */}\r\n                <Divider\r\n                  type=\"vertical\"\r\n                  style={{\r\n                    height: \"80px\",\r\n                    borderColor: \"rgba(255,255,255,0.3)\",\r\n                    margin: \"0 16px\",\r\n                  }}\r\n                />\r\n\r\n                {/* 右侧：最近活动信息 - 使用 Space 组件 */}\r\n                <Space\r\n                  direction=\"vertical\"\r\n                  size={10}\r\n                  style={{\r\n                    flex: \"0 0 300px\",\r\n                    paddingLeft: 20,\r\n                    width: 300,\r\n                    minWidth: 300,\r\n                  }}\r\n                >\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                      最后登录时间\r\n                    </Text>\r\n                    <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                      {userInfo.lastLoginTime || \"暂无记录\"}\r\n                    </Text>\r\n                  </Space>\r\n                  <Space direction=\"vertical\" size={4}>\r\n                    <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                      最后登录团队\r\n                    </Text>\r\n                    <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                      {userInfo.lastLoginTeam || \"暂无记录\"}\r\n                    </Text>\r\n                  </Space>\r\n                </Space>\r\n              </Flex>\r\n            </Card>\r\n          </Spin>\r\n        )}\r\n      </Card>\r\n\r\n      {/* 修改资料模态框 */}\r\n      <Modal\r\n        title=\"修改个人资料\"\r\n        open={editProfileModalVisible}\r\n        onCancel={() => {\r\n          setEditProfileModalVisible(false);\r\n          setCurrentStep(0);\r\n        }}\r\n        footer={[\r\n          currentStep === 1 && (\r\n            <Button key=\"back\" onClick={() => setCurrentStep(0)}>\r\n              上一步\r\n            </Button>\r\n          ),\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              if (currentStep === 0) {\r\n                editProfileForm.validateFields().then(() => {\r\n                  setCurrentStep(1);\r\n                });\r\n              } else {\r\n                editProfileForm.validateFields().then((values) => {\r\n                  console.log(\"个人资料表单值:\", values);\r\n                  // 提交表单，这里简化处理，只输出到控制台\r\n                  setEditProfileModalVisible(false);\r\n                  setCurrentStep(0);\r\n                });\r\n              }\r\n            }}\r\n          >\r\n            {currentStep === 0 ? \"下一步\" : \"确定\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Steps current={currentStep} style={{ marginBottom: 16 }}>\r\n          <Step title=\"填写信息\" />\r\n          <Step title=\"安全验证\" />\r\n        </Steps>\r\n\r\n        <Form form={editProfileForm} layout=\"vertical\" requiredMark={false}>\r\n          {currentStep === 0 ? (\r\n            <>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"用户名\"\r\n                rules={[{ required: true, message: \"请输入用户名\" }]}\r\n              >\r\n                <Input placeholder=\"请输入用户名\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"邮箱\"\r\n                rules={[\r\n                  { required: true, message: \"请输入邮箱地址\" },\r\n                  { type: \"email\", message: \"请输入有效的邮箱地址\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入邮箱地址\" />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"telephone\"\r\n                label=\"手机号\"\r\n                rules={[\r\n                  { required: true, message: \"请输入手机号\" },\r\n                  { pattern: /^1\\d{10}$/, message: \"请输入有效的手机号\" },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入手机号\" />\r\n              </Form.Item>\r\n            </>\r\n          ) : (\r\n            /* 验证码步骤 - 使用 Space 组件 */\r\n            <Space direction=\"vertical\" align=\"center\" style={{ width: \"100%\" }}>\r\n              <Text style={{ margin: \"12px 0\" }}>\r\n                验证码已发送至您的手机号{\" \"}\r\n                <Text strong>{editProfileForm.getFieldValue(\"telephone\")}</Text>\r\n              </Text>\r\n              <Form.Item\r\n                name=\"verificationCode\"\r\n                label=\"验证码\"\r\n                rules={[{ required: true, message: \"请输入验证码\" }]}\r\n                style={{ textAlign: \"center\" }}\r\n              >\r\n                <Input\r\n                  placeholder=\"请输入6位验证码\"\r\n                  maxLength={6}\r\n                  style={{ width: \"50%\", textAlign: \"center\" }}\r\n                />\r\n              </Form.Item>\r\n              <Button type=\"link\" style={{ padding: 0 }}>\r\n                重新发送验证码\r\n              </Button>\r\n            </Space>\r\n          )}\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 订阅套餐模态框 */}\r\n      <Modal\r\n        title=\"订阅套餐\"\r\n        open={subscriptionModalVisible}\r\n        onCancel={() => setSubscriptionModalVisible(false)}\r\n        footer={null}\r\n        width={800}\r\n      >\r\n        {/* 当前套餐信息 - 使用 Card 组件 */}\r\n        <Card\r\n          size=\"small\"\r\n          style={{\r\n            background: \"#f9f9f9\",\r\n            marginBottom: 16,\r\n          }}\r\n        >\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Text strong>当前套餐: </Text>\r\n            <Tag color=\"green\" style={{ marginLeft: 8, fontSize: 13 }}>\r\n              {\r\n                subscriptionPlans.find(\r\n                  (p) => p.id === currentSubscription.planId\r\n                )?.name\r\n              }\r\n            </Tag>\r\n            <Text type=\"secondary\">\r\n              到期时间: {currentSubscription.expires}\r\n            </Text>\r\n          </Flex>\r\n        </Card>\r\n\r\n        <Row gutter={24}>\r\n          {subscriptionPlans.map((plan) => (\r\n            <Col span={8} key={plan.id}>\r\n              {/* 套餐卡片 - 使用 Card 组件 */}\r\n              <Card\r\n                style={{\r\n                  height: \"100%\",\r\n                  border: `1px solid ${\r\n                    plan.id === currentSubscription.planId\r\n                      ? \"#52c41a\"\r\n                      : \"#d9d9d9\"\r\n                  }`,\r\n                  position: \"relative\",\r\n                }}\r\n                styles={{ body: { padding: 16 } }}\r\n              >\r\n                {plan.id === currentSubscription.planId && (\r\n                  <Tag\r\n                    color=\"green\"\r\n                    style={{\r\n                      position: \"absolute\",\r\n                      top: -10,\r\n                      right: -10,\r\n                      borderRadius: 2,\r\n                      boxShadow: \"0 2px 8px rgba(0,0,0,0.1)\",\r\n                    }}\r\n                  >\r\n                    当前套餐\r\n                  </Tag>\r\n                )}\r\n                <Title\r\n                  level={4}\r\n                  style={{ textAlign: \"center\", margin: \"12px 0 8px\" }}\r\n                >\r\n                  {plan.name}\r\n                </Title>\r\n                <Flex vertical align=\"center\" style={{ marginBottom: 12 }}>\r\n                  {plan.price > 0 ? (\r\n                    <>\r\n                      <Title level={2} style={{ marginBottom: 0 }}>\r\n                        ¥{plan.price}\r\n                      </Title>\r\n                      <Text type=\"secondary\">/月</Text>\r\n                    </>\r\n                  ) : (\r\n                    <Title\r\n                      level={2}\r\n                      style={{ color: \"#52c41a\", marginBottom: 0 }}\r\n                    >\r\n                      免费\r\n                    </Title>\r\n                  )}\r\n                  <Text type=\"secondary\" style={{ marginTop: 4 }}>\r\n                    {plan.description}\r\n                  </Text>\r\n                </Flex>\r\n\r\n  \r\n\r\n                {/* 功能列表 - 使用 Space 组件 */}\r\n                <Space direction=\"vertical\" size={6} style={{ minHeight: 170, width: \"100%\" }}>\r\n                  {plan.features.map((feature, index) => (\r\n                    <Space key={index} align=\"start\">\r\n                      <CheckOutlined\r\n                        style={{\r\n                          color: \"#52c41a\",\r\n                          marginTop: 4,\r\n                        }}\r\n                      />\r\n                      <Text>{feature}</Text>\r\n                    </Space>\r\n                  ))}\r\n                </Space>\r\n\r\n                {plan.id !== currentSubscription.planId ? (\r\n                  <Button\r\n                    type=\"primary\"\r\n                    block\r\n                    style={{\r\n                      marginTop: 12,\r\n                      boxShadow: \"0 2px 8px rgba(24, 144, 255, 0.3)\",\r\n                    }}\r\n                    onClick={() => {\r\n                      console.log(\"选择套餐:\", plan);\r\n                      setSubscriptionModalVisible(false);\r\n                    }}\r\n                  >\r\n                    立即订阅\r\n                  </Button>\r\n                ) : (\r\n                  <Button\r\n                    block\r\n                    style={{\r\n                      marginTop: 12,\r\n                      background: \"#f6ffed\",\r\n                      borderColor: \"#b7eb8f\",\r\n                      color: \"#389e0d\",\r\n                    }}\r\n                    disabled\r\n                  >\r\n                    当前套餐\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            </Col>\r\n          ))}\r\n        </Row>\r\n\r\n        <Flex justify=\"center\" style={{ marginTop: 20 }}>\r\n          <Text type=\"secondary\">订阅服务自动续费，可随时取消</Text>\r\n        </Flex>\r\n      </Modal>\r\n\r\n      {/* 退出登录确认模态框 */}\r\n      <Modal\r\n        title=\"确认退出登录\"\r\n        open={logoutModalVisible}\r\n        onCancel={() => setLogoutModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setLogoutModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"confirm\"\r\n            type=\"primary\"\r\n            danger\r\n            loading={logoutLoading}\r\n            onClick={handleLogout}\r\n          >\r\n            确认退出\r\n          </Button>,\r\n        ]}\r\n        width={400}\r\n      >\r\n        {/* 退出登录确认内容 - 使用 Space 组件 */}\r\n        <Space direction=\"vertical\" align=\"center\" style={{ width: \"100%\", padding: \"20px 0\" }}>\r\n          <LogoutOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />\r\n          <Text strong style={{ fontSize: 16 }}>您确定要退出登录吗？</Text>\r\n          <Text type=\"secondary\" style={{ textAlign: \"center\" }}>\r\n            退出后您需要重新登录才能继续使用系统\r\n          </Text>\r\n        </Space>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UserProfileCard;", "import React from \"react\";\nimport { <PERSON><PERSON>, <PERSON>, Col, Row } from \"antd\";\n \nimport TodoManagement from './TodoManagement';\nimport TeamListCard from './TeamListCard';\nimport UserProfileCard from \"./UserProfileCard\";\n\nconst FleetManagementDashboard: React.FC = () => {\n  return (\n    <div style={{ minHeight: \"100vh\", background: \"#f5f8ff\", padding: \"24px\" }}>\n      {/* 大的容器区域 */}\n      <Card\n        style={{\n          width: \"100%\",\n          minHeight: \"calc(100vh - 48px)\",\n          borderRadius: \"12px\",\n          boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.1)\"\n        }}\n        styles={{ body: { padding: \"24px\" } }}\n      >\n        <Row gutter={[24, 24]}>\n          {/* 个人信息卡片 */}\n          <Col xs={24}>\n            <UserProfileCard />\n          </Col>\n\n          {/* 待办事项 */}\n          <Col xs={24} lg={12}>\n            <TodoManagement />\n          </Col>\n\n          {/* 团队列表 */}\n          <Col xs={24} lg={12}>\n            <TeamListCard />\n          </Col>\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default FleetManagementDashboard;", "/**\n * TODO服务\n */\n\nimport { apiRequest } from '@/utils/request';\nimport type {\n  TodoResponse,\n  CreateTodoRequest,\n  UpdateTodoRequest,\n  TodoStatsResponse,\n} from '@/types/api';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(id: number, request: UpdateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(`/todos/${id}`, request);\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;4BAoaA;;;eAAA;;;;;;0DApa2C;6BAUpC;6BACqB;iCACA;4BACM;8BAU3B;;;;;;;;;;AAEP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAGlC,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;AAsBhB,CAAC;AAED,MAAM,eAAyB;;IAE7B,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;IAEtE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAG7C,IAAA,gBAAS,EAAC;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;gBACzD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAGL,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI,YAAW,wBAAA,kCAAA,YAAa,EAAE,GAAE;YAC9B,aAAO,CAAC,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE;YAAO;YAGvD,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,QAAQ;gBACjF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC;gBAGpC,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;oBACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;wBACnD,aAAa,aAAa;wBAC1B,aAAa,aAAa;qBAC3B;oBAGD,IAAI,eAAe,YAAY,EAAE,KAAK,QAAQ;wBAC5C,MAAM,gBAAgB;4BACpB,GAAG,YAAY;4BACf;4BACA;wBACF;wBAGA,WAAW;4BACT,YAAO,CAAC,IAAI,CAAC;wBACf,GAAG;oBACL,OAAO;wBACL,QAAQ,KAAK,CAAC;wBACd,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,aAAO,CAAC,KAAK,CAAC;gBAChB;qBAGA,YAAO,CAAC,IAAI,CAAC;YAEjB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,OACE;;YAEE,2BAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAO;;;;;;YAEjD,2BAAC,UAAI;gBACH,WAAU;gBACV,OAAO;oBACL,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,YAAY;gBACd;gBACA,OACE,2BAAC,UAAI;oBAAC,SAAQ;oBAAgB,OAAM;8BAClC,2BAAC;wBAAM,OAAO;wBAAG,OAAO;4BACtB,QAAQ;4BACR,YAAY;4BACZ,sBAAsB;4BACtB,qBAAqB;4BACrB,YAAY;wBACd;kCAAG;;;;;;;;;;;0BAMR,QACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;2BAG5B,2BAAC,UAAI;oBAAC,UAAU;8BACd,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;gCAyKI,aAkBA,cAkBA,cAkBA;mCA9Nf,2BAAC,UAAI,CAAC,IAAI;0CACR,2BAAC,UAAI;oCACH,WAAU;oCACV,OAAO;wCACL,YAAY,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GACnC,8CACA;wCACJ,cAAc;wCACd,WAAW,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAClC,uCACA;wCACJ,OAAO;wCACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;wCACjE,YAAY;wCACZ,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAC/B,sBACA;wCACJ,SAAS;wCACT,UAAU;wCACV,UAAU;oCACZ;oCACA,SAAS;oCACT,cAAc,CAAC;wCACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;oCACF;8CAIA,2BAAC,UAAI;wCAAC,SAAQ;wCAAgB,OAAM;wCAAS,OAAO;4CAAE,OAAO;wCAAO;;4CAElE,2BAAC,UAAI;gDAAC,QAAQ;gDAAC,KAAK;gDAAG,OAAO;oDAAE,MAAM;gDAAE;;oDAEtC,2BAAC,UAAI;wDAAC,OAAM;wDAAS,KAAK;;4DACxB,2BAAC;gEACC,OAAO;oEACL,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;oEAClD,SAAS;oEACT,cAAc;oEACd,YAAY;gEACd;gEACA,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;gEAClD,cAAc,CAAC;oEACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gEAEvC;gEACA,cAAc,CAAC;oEACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gEAEvC;;oEAEA,2BAAC;wEACC,MAAM;wEACN,OAAO;4EACL,UAAU;4EACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;4EACjD,YAAY;wEACd;kFAEC,KAAK,IAAI;;;;;;oEAEX,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,IAC1B,2BAAC,oBAAa;wEACZ,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;4EACZ,eAAe;4EACf,SAAS;4EACT,YAAY;wEACd;;;;;;;;;;;;4DAML,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,IAC1B,2BAAC;gEAAK,OAAO;oEACX,YAAY;oEACZ,OAAO;oEACP,SAAS;oEACT,cAAc;oEACd,UAAU;oEACV,YAAY;gEACd;0EAAG;;;;;;4DAKJ,oBAAoB,KAAK,EAAE,IAC1B,2BAAC,UAAI;gEAAC,OAAM;gEAAS,KAAK;;oEACxB,2BAAC,UAAI;wEAAC,MAAK;;;;;;oEACX,2BAAC;wEAAK,OAAO;4EAAE,UAAU;4EAAI,OAAO;wEAAO;kFAAG;;;;;;;;;;;;;;;;;;oDAMpD,2BAAC,UAAI;wDAAC,OAAM;wDAAS,KAAK;;4DACxB,2BAAC,aAAO;gEAAC,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;0EACzE,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;wEACxB,2BAAC,0BAAmB;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEAC7D,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAU;sFAC3C,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;4DAKnD,2BAAC,aAAO;gEAAC,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;0EAC1C,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;wEACxB,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEACtD,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAU;;gFAC3C,KAAK,WAAW;gFAAC;;;;;;;;;;;;;;;;;;4DAMxB,2BAAC;gEAAK,OAAO;oEACX,YAAY,KAAK,SAAS,GAAG,YAAY;oEACzC,OAAO;oEACP,SAAS;oEACT,cAAc;oEACd,UAAU;oEACV,YAAY;oEACZ,SAAS;oEACT,YAAY;oEACZ,KAAK;gEACP;0EACG,KAAK,SAAS,GACb;;wEACE,2BAAC,oBAAa;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;mFAI3C;;wEACE,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,UAAU;4EAAE;;;;;;wEAAK;;;;;;;;;;;;;;;;;;;;4CASlD,2BAAC,UAAI;gDAAC,KAAK;gDAAG,OAAM;;oDAElB,2BAAC;wDAAI,OAAO;4DACV,YAAY;4DACZ,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,WAAW;4DACX,UAAU;wDACZ;kEACE,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,OAAM;4DAAS,KAAK;;gEACjC,2BAAC,kBAAW;oEAAC,OAAO;wEAAE,OAAO;wEAAW,UAAU;oEAAG;;;;;;gEACrD,2BAAC;oEAAK,MAAM;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAW,YAAY;oEAAE;8EACjE,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;gEAE3B,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAG,OAAO;oEAAO;8EAAG;;;;;;;;;;;;;;;;;oDAKjD,2BAAC;wDAAI,OAAO;4DACV,YAAY;4DACZ,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,WAAW;4DACX,UAAU;wDACZ;kEACE,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,OAAM;4DAAS,KAAK;;gEACjC,2BAAC,mBAAY;oEAAC,OAAO;wEAAE,OAAO;wEAAW,UAAU;oEAAG;;;;;;gEACtD,2BAAC;oEAAK,MAAM;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAW,YAAY;oEAAE;8EACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;gEAE5B,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAG,OAAO;oEAAO;8EAAG;;;;;;;;;;;;;;;;;oDAKjD,2BAAC;wDAAI,OAAO;4DACV,YAAY;4DACZ,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,WAAW;4DACX,UAAU;wDACZ;kEACE,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,OAAM;4DAAS,KAAK;;gEACjC,2BAAC,gCAAyB;oEAAC,OAAO;wEAAE,OAAO;wEAAW,UAAU;oEAAG;;;;;;gEACnE,2BAAC;oEAAK,MAAM;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAW,YAAY;oEAAE;8EACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;gEAE3B,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAG,OAAO;oEAAO;8EAAG;;;;;;;;;;;;;;;;;oDAKjD,2BAAC;wDAAI,OAAO;4DACV,YAAY;4DACZ,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,WAAW;4DACX,UAAU;wDACZ;kEACE,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,OAAM;4DAAS,KAAK;;gEACjC,2BAAC,gCAAyB;oEAAC,OAAO;wEAAE,OAAO;wEAAW,UAAU;oEAAG;;;;;;gEACnE,2BAAC;oEAAK,MAAM;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;wEAAW,YAAY;oEAAE;8EACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;gEAE1B,2BAAC;oEAAK,OAAO;wEAAE,UAAU;wEAAG,OAAO;oEAAO;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcvE;GA9WM;;QAOsC,aAAQ;;;KAP9C;IAgXN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCyNf;;;eAAA;;;;;;0DA7nB2C;6BAqBpC;6BACqB;8BAUrB;;;;;;;;;;AAEP,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,iBAAgD,CAAC;;IAErD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;QAC5D,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAGlD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAGlE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAG7C,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC;gBAGZ,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAA;oBACpD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO,EAAE;gBACX;gBAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAA;oBACpD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;wBACL,mBAAmB;wBACnB,qBAAqB;wBACrB,kBAAkB;wBAClB,YAAY;wBACZ,gBAAgB;wBAChB,sBAAsB;oBACxB;gBACF;gBAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAa;gBAErE,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,iBAAiB;gBACjB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAGL,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC;QAElD,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;QAG3D,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;QAGT,OAAO;IACT;IAGA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC9C,IAAI,CAAC,MAAM;gBACT,aAAO,CAAC,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;YAC1C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC;YAE1D,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;gBAAE,QAAQ;YAAU;YAGrD,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;YAKtD,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAE7B;YAEA,aAAO,CAAC,OAAO,CAAC,cAAc,IAAI,UAAU;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB;gBAAE;gBAAe;YAAO;YAE5D,IAAI,eAAe;gBAEjB,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;oBAC9D,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;gBAG9C,aAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBAEL,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;oBAC3C,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBAAiB;oBAAC;uBAAY;iBAAc;gBAC5C,aAAO,CAAC,OAAO,CAAC;YAClB;YAGA,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAE7B;YAGA,oBAAoB;YACpB,iBAAiB;YACjB,SAAS,WAAW;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,SAAS,gBAAgB,OAAO;YACtC,aAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,eAAe,CAAC;QAC1C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,MAAM,iBAAW,CAAC,UAAU,CAAC;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAG5D,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAE7B;YAEA,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OACE,2BAAC,UAAI;QACH,WAAU;QACV,OAAO;YACL,cAAc;YACd,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA,OACE,2BAAC,UAAI;YAAC,SAAQ;YAAgB,OAAM;sBAClC,2BAAC;gBAAK,MAAM;0BAAC;;;;;;;;;;;;YAKjB,2BAAC,UAAI;gBACH,SAAQ;gBACR,OAAM;gBACN,OAAO;oBACL,cAAc;oBACd,KAAK;oBACL,UAAU;oBACV,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;gBACV;;oBAGA,2BAAC,UAAI;wBAAC,OAAM;wBAAS,KAAK;wBAAI,OAAO;4BAAE,MAAM;4BAAG,UAAU;wBAAI;;4BAC5D,2BAAC,WAAK,CAAC,MAAM;gCACX,aAAY;gCACZ,UAAU;gCACV,QAAQ,2BAAC,qBAAc;;;;;gCACvB,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,OAAO;oCACL,MAAM;oCACN,UAAU;gCACZ;gCACA,MAAK;;;;;;4BAGP,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAM,2BAAC,mBAAY;;;;;gCACnB,SAAS;oCACP,iBAAiB;oCACjB,SAAS,WAAW;oCACpB,oBAAoB;gCACtB;gCACA,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;oCACX,YAAY;oCACZ,UAAU;gCACZ;gCACA,MAAK;0CACN;;;;;;;;;;;;oBAMH,2BAAC,UAAI;wBAAC,OAAM;wBAAS,KAAK;wBAAI,OAAO;4BAAE,YAAY;wBAAE;;4BAEnD,2BAAC,WAAK;gCAAC,MAAM;;oCACX,2BAAC,aAAO;wCAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB,CAAC,CAAC,CAAC;kDACvD,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;gDACxB,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;gDAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;;wDAAG;wDAC5D,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;oCAKrC,2BAAC,aAAO;wCAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,mBAAmB,CAAC,CAAC,CAAC;kDACzD,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;gDACxB,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;gDAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;;wDAAG;wDAC5D,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;oCAKvC,2BAAC,aAAO;wCAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC;kDACtD,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;gDACxB,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;gDAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;;wDAAG;wDAC5D,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;4BAMtC,2BAAC,aAAO;gCAAC,MAAK;gCAAW,OAAO;oCAAE,QAAQ;oCAAI,iBAAiB;gCAAU;;;;;;4BAGzE,2BAAC,aAAO;gCAAC,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;0CAC7G,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;;wCACxB,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,YAAY;gDAAK,OAAO;4CAAU;sDAAG;;;;;;wCAGlE,2BAAC,cAAQ;4CACP,SAAS,UAAU,oBAAoB;4CACvC,MAAK;4CACL,OAAO;gDAAE,OAAO;4CAAI;4CACpB,aAAY;4CACZ,UAAU;;;;;;wCAEZ,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,YAAY;gDAAK,OAAO;4CAAU;;gDAC5D,UAAU,oBAAoB;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ1C,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU,CAAC,MACT,aAAa;gBAEf,MAAK;gBACL,OAAO;oBAAE,cAAc;gBAAE;;oBAEzB,2BAAC;wBAAQ,KAAI;uBAAS;;;;;oBACtB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;oBACvB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;;;;;;;YAIxB,QACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;uBAG5B,2BAAC,UAAI;gBAAC,UAAU;;oBACd,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;4BACf,OACE,2BAAC,UAAI,CAAC,IAAI;gCACR,WAAU;gCACV,OAAO;oCACL,SAAS;oCACT,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;oCACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,UACL,CAAC;oCACF,WAAW;gCACb;0CAEA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,OAAO;wCAAE,OAAO;oCAAO;;wCAEnD,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;;gDAClB,KAAK,MAAM,KAAK,IACf,2BAAC,UAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;8DAEA,2BAAC,oBAAa;wDACZ,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;6DAIzC,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,UACL,CAAC;oDACJ;;;;;;gDAIJ,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,WAAW;oDACb;;;;;;;;;;;;wCAKJ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAO;gDAAE,MAAM;4CAAE;;gDAC9B,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YACE,KAAK,QAAQ,KAAK,IAAI,MAAM;wDAC9B,gBAAgB,KAAK,MAAM,KAAK,IAC5B,iBACA;wDACJ,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;oDACzC;8DAEC,KAAK,KAAK;;;;;;gDAIb,2BAAC,WAAK;oDAAC,OAAM;oDAAS,MAAM;oDAAG,OAAO;wDAAE,WAAW;oDAAE;;wDACnD,2BAAC,uBAAgB;4DACf,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;wDAEF,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAAE,UAAU;4DAAG;;gEAAG;gEACxC,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;wCAMxD,2BAAC,cAAQ;4CACP,SAAS;gDAAC;6CAAQ;4CAClB,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,OAAO,KAAK,MAAM,KAAK,IAAI,UAAU;wDACrC,MACE,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;gEACvC,UAAU;4DACZ;;;;;;oDAGN;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,MAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;oDAChD;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,MAAM,2BAAC,qBAAc;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDAChD,QAAQ;oDACV;iDACD;gDACD,SAAS,CAAC,EAAE,GAAG,EAAE;oDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;yDACzB,IAAI,QAAQ,QAAQ;wDACzB,iBAAiB,KAAK,EAAE;wDACxB,SAAS,cAAc,CAAC;4DACtB,MAAM,KAAK,KAAK;4DAChB,UAAU,KAAK,QAAQ;wDACzB;wDACA,oBAAoB;oDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;gDAE5B;4CACF;sDAEA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,MAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,OAAO;oDAAI,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;wBAM3C;;;;;;oBAIF,2BAAC,WAAK;wBACJ,OAAO,gBAAgB,WAAW;wBAClC,MAAM;wBACN,UAAU;4BACR,oBAAoB;4BACpB,SAAS,WAAW;wBACtB;wBACA,MAAM;4BACJ,SAAS,MAAM;wBACjB;wBACA,QAAQ;wBACR,cAAc;wBACd,QAAQ;4BACN,2BAAC,YAAM;gCAAc,SAAS,IAAM,oBAAoB;0CAAQ;+BAApD;;;;;4BAGZ,2BAAC,YAAM;gCAEL,MAAK;gCACL,SAAS;oCACP,SAAS,MAAM;gCACjB;gCACA,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;gCACb;0CAEC,gBAAgB,SAAS;+BAXtB;;;;;yBAaP;kCAED,2BAAC,UAAI;4BACH,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,cAAa;;gCAEb,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;gCAI7B,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;oCACd,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,2BAAC,YAAM;wCACL,MAAK;wCACL,SAAS;4CACP;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;yCAC3B;wCACD,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;GA/kBM;;QAgBe,UAAI,CAAC;;;KAhBpB;IAilBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCkOf;;;eAAA;;;;;;8BAr1BO;6BAoBA;0DACoC;6BACf;iCACA;4BACM;;;;;;;;;;AAGlC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAK;AAEtB,MAAM,kBAA4B;QAypBlB;;IAvpBd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;QAClE,MAAM;QACN,UAAU;QACV,OAAO;QACP,OAAO;QACP,WAAW;QACX,cAAc;QACd,eAAe;QACf,eAAe;QACf,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAGlE,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;QAC5E,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;IAG5D,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAU;gBAAW;gBAAU;aAAS;QACrD;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAGD,MAAM,sBAAsB;QAC1B,QAAQ;QACR,SAAS;IACX;IAGA,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,IAAA,eAAQ,EAAC;IACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,IAAA,eAAQ,EAAC;IACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,gBAAgB,GAAG,UAAI,CAAC,OAAO;IAEtC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAGrC,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAGZ,MAAM,oBAAoB,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;oBACjE,QAAQ,KAAK,CAAC,eAAe;oBAC7B,iBAAiB;oBACjB,OAAO;gBACT;gBAEA,MAAM,eAAe,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;oBAC5D,QAAQ,KAAK,CAAC,aAAa;oBAC3B,cAAc;oBACd,OAAO;gBACT;gBAEA,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAmB;iBAAa;gBAE/E,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,YAAY;oBACZ,iBAAiB;gBACnB;gBAEA,IAAI,OAAO;oBACT,QAAQ,GAAG,CAAC,6BAA6B;oBACzC,iBAAiB;oBACjB,cAAc;gBAChB;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,iBAAiB;gBACjB,cAAc;YAChB,SAAU;gBACR,mBAAmB;gBACnB,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAGL,MAAM,eAAe;QACnB,IAAI;YACF,iBAAiB;YAGjB,MAAM,qBAAW,CAAC,MAAM;YAGxB,IAAI,iBACF,MAAM,gBAAgB;gBACpB,aAAa;gBACb,aAAa;YACf;YAIF,YAAO,CAAC,IAAI,CAAC;QAEf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YAEzB,IAAI,iBACF,MAAM,gBAAgB;gBACpB,aAAa;gBACb,aAAa;YACf;YAEF,YAAO,CAAC,IAAI,CAAC;QACf,SAAU;YACR,iBAAiB;YACjB,sBAAsB;QACxB;IACF;IAEA,OACE;;YACE,2BAAC,UAAI;gBACH,WAAU;gBAAiB,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAG;gBAAE;0BAK1D,gBACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;2BAG5B,2BAAC,UAAI;oBAAC,UAAU;8BAEd,2BAAC,UAAI;wBACH,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,OAAO;4BACP,UAAU;4BACV,UAAU;4BACV,QAAQ;4BACR,QAAQ;wBACV;wBACA,QAAQ;4BAAE,MAAM;gCAAE,SAAS;gCAAI,QAAQ;4BAAO;wBAAE;;4BAGhD,2BAAC,WAAK;gCACJ,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,OAAO;oCACP,QAAQ;gCACV;gCACA,MAAM;;oCAEN,2BAAC,aAAO;wCAAC,OAAM;kDACb,2BAAC,cAAQ;4CACP,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,MAAM,2BAAC,mBAAY;;;;;wDACnB,OAAO;wDACP,SAAS;4DACP,2BAA2B;4DAC3B,eAAe;4DACf,gBAAgB,cAAc,CAAC;gEAC7B,MAAM,SAAS,IAAI;gEACnB,OAAO,SAAS,KAAK;gEACrB,WAAW,SAAS,KAAK,IAAI,SAAS,SAAS;4DACjD;wDACF;oDACF;oDACA;wDACE,KAAK;wDACL,MAAM,2BAAC,kBAAW;;;;;wDAClB,OAAO;wDACP,SAAS,IAAM,4BAA4B;oDAC7C;iDACD;4CACH;4CACA,SAAS;gDAAC;6CAAQ;4CAClB,WAAU;sDAEV,2BAAC,YAAM;gDACL,MAAK;gDACL,OAAM;gDACN,MAAM,2BAAC,sBAAe;;;;;gDACtB,OAAO;oDACL,OAAO;oDACP,iBAAiB;oDACjB,QAAQ;oDACR,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;;;;;;;;;;;;;;;;oCAKN,2BAAC,aAAO;wCAAC,OAAM;kDACb,2BAAC,YAAM;4CACL,MAAK;4CACL,OAAM;4CACN,MAAM,2BAAC,qBAAc;;;;;4CACrB,SAAS,IAAM,sBAAsB;4CACrC,OAAO;gDACL,OAAO;gDACP,iBAAiB;gDACjB,QAAQ;gDACR,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAChC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAChC;;;;;;;;;;;;;;;;;4BAKN,2BAAC;gCACC,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,OAAO;oCACP,OAAO;oCACP,QAAQ;oCACR,YAAY;oCACZ,cAAc;gCAChB;;;;;;4BAEF,2BAAC;gCACC,OAAO;oCACL,UAAU;oCACV,QAAQ;oCACR,MAAM;oCACN,OAAO;oCACP,QAAQ;oCACR,YAAY;oCACZ,cAAc;gCAChB;;;;;;4BAEF,2BAAC;gCACC,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,OAAO;oCACP,OAAO;oCACP,QAAQ;oCACR,YAAY;oCACZ,cAAc;oCACd,WAAW;gCACb;;;;;;4BAIF,2BAAC,UAAI;gCACH,OAAM;gCACN,KAAK;gCACL,OAAO;oCAAE,UAAU;oCAAY,QAAQ;oCAAG,OAAO;gCAAO;;oCAGxD,2BAAC,UAAI;wCAAC,OAAM;wCAAS,OAAO;4CAAE,MAAM;4CAAa,UAAU;wCAAI;;4CAE7D,2BAAC,YAAM;gDACL,MAAM;gDACN,OAAM;gDACN,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,UAAU;oDACV,YAAY;oDACZ,QAAQ;gDACV;0DAEC,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,2BAAC,mBAAY;;;;;;;;;;4CAIxE,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;oDAChC,2BAAC;wDACC,OAAO;wDACP,OAAO;4DACL,QAAQ;4DACR,OAAO;4DACP,UAAU;4DACV,YAAY;wDACd;kEAEC,SAAS,IAAI,IAAI;;;;;;oDAIpB,2BAAC,WAAK;wDAAC,WAAU;wDAAW,MAAM;;4DAC/B,SAAS,KAAK,IACb,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;oEACpB,2BAAC,mBAAY;wEAAC,OAAO;4EAAE,UAAU;4EAAI,OAAO;wEAAwB;;;;;;oEACpE,2BAAC;wEAAK,OAAO;4EAAE,OAAO;4EAAyB,UAAU;wEAAG;kFACzD,SAAS,KAAK;;;;;;;;;;;;4DAInB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,KACnC,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;oEACpB,2BAAC,oBAAa;wEAAC,OAAO;4EAAE,UAAU;4EAAI,OAAO;wEAAwB;;;;;;oEACrE,2BAAC;wEAAK,OAAO;4EAAE,OAAO;4EAAyB,UAAU;wEAAG;kFACzD,SAAS,KAAK,IAAI,SAAS,SAAS;;;;;;;;;;;;;;;;;;oDAO5C,SAAS,YAAY,IACpB,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAyB,YAAY;wDAAI;;4DAAG;4DACzE,SAAS,YAAY;;;;;;;;;;;;;;;;;;;oCAOlC,2BAAC,aAAO;wCACN,MAAK;wCACL,OAAO;4CACL,QAAQ;4CACR,aAAa;4CACb,QAAQ;wCACV;;;;;;oCAIF,2BAAC,UAAI;wCACH,QAAQ;wCACR,OAAO;4CACL,MAAM;wCACR;;4CAGA,2BAAC,WAAK;gDACJ,OAAM;gDACN,OAAO;oDACL,gBAAgB;oDAChB,cAAc;oDACd,QAAQ;gDACV;;oDAEA,2BAAC,uBAAgB;wDACf,OAAO;4DACL,UAAU;4DACV,OAAO;wDACT;;;;;;oDAEF,2BAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAyB,UAAU;4DAAI,YAAY;4DAAK,YAAY;wDAAE;kEAAG;;;;;;;;;;;;4CAKhG,aACC,2BAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAI,OAAO;gDAAwB;0DAAG;;;;;uDAI/D,2BAAC,UAAI;gDAAC,UAAU;0DAEd,2BAAC,UAAI;oDAAC,SAAQ;oDAAe,OAAM;;wDACjC,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;gEAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,QAAQ;;;;;;gEAEzB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;wDAIL,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;gEAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,SAAS;;;;;;gEAE1B,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;wDAIL,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;gEAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,QAAQ;;;;;;gEAEzB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;wDAIL,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;gEAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,MAAM;;;;;;gEAEvB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAUb,2BAAC,aAAO;wCACN,MAAK;wCACL,OAAO;4CACL,QAAQ;4CACR,aAAa;4CACb,QAAQ;wCACV;;;;;;oCAIF,2BAAC,WAAK;wCACJ,WAAU;wCACV,MAAM;wCACN,OAAO;4CACL,MAAM;4CACN,aAAa;4CACb,OAAO;4CACP,UAAU;wCACZ;;4CAEA,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;oDAChC,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAyB,YAAY;wDAAI;kEAAG;;;;;;oDAGhF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAS,YAAY;4DAAK,YAAY;wDAAI;kEAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;4CAG/B,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;oDAChC,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAyB,YAAY;wDAAI;kEAAG;;;;;;oDAGhF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAS,YAAY;4DAAK,YAAY;wDAAI;kEAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW3C,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,2BAA2B;oBAC3B,eAAe;gBACjB;gBACA,QAAQ;oBACN,gBAAgB,KACd,2BAAC,YAAM;wBAAY,SAAS,IAAM,eAAe;kCAAI;uBAAzC;;;;;oBAId,2BAAC,YAAM;wBAEL,MAAK;wBACL,SAAS;4BACP,IAAI,gBAAgB,GAClB,gBAAgB,cAAc,GAAG,IAAI,CAAC;gCACpC,eAAe;4BACjB;iCAEA,gBAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;gCACrC,QAAQ,GAAG,CAAC,YAAY;gCAExB,2BAA2B;gCAC3B,eAAe;4BACjB;wBAEJ;kCAEC,gBAAgB,IAAI,QAAQ;uBAjBzB;;;;;iBAmBP;;oBAED,2BAAC,WAAK;wBAAC,SAAS;wBAAa,OAAO;4BAAE,cAAc;wBAAG;;4BACrD,2BAAC;gCAAK,OAAM;;;;;;4BACZ,2BAAC;gCAAK,OAAM;;;;;;;;;;;;oBAGd,2BAAC,UAAI;wBAAC,MAAM;wBAAiB,QAAO;wBAAW,cAAc;kCAC1D,gBAAgB,IACf;;gCACE,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,2BAAC,WAAK;wCAAC,aAAY;;;;;;;;;;;gCAErB,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAU;wCACrC;4CAAE,MAAM;4CAAS,SAAS;wCAAa;qCACxC;8CAED,2BAAC,WAAK;wCAAC,aAAY;;;;;;;;;;;gCAErB,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAS;wCACpC;4CAAE,SAAS;4CAAa,SAAS;wCAAY;qCAC9C;8CAED,2BAAC,WAAK;wCAAC,aAAY;;;;;;;;;;;;2CAKvB,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,OAAO;gCAAE,OAAO;4BAAO;;gCAChE,2BAAC;oCAAK,OAAO;wCAAE,QAAQ;oCAAS;;wCAAG;wCACpB;wCACb,2BAAC;4CAAK,MAAM;sDAAE,gBAAgB,aAAa,CAAC;;;;;;;;;;;;gCAE9C,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;oCAC9C,OAAO;wCAAE,WAAW;oCAAS;8CAE7B,2BAAC,WAAK;wCACJ,aAAY;wCACZ,WAAW;wCACX,OAAO;4CAAE,OAAO;4CAAO,WAAW;wCAAS;;;;;;;;;;;gCAG/C,2BAAC,YAAM;oCAAC,MAAK;oCAAO,OAAO;wCAAE,SAAS;oCAAE;8CAAG;;;;;;;;;;;;;;;;;;;;;;;YASnD,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,4BAA4B;gBAC5C,QAAQ;gBACR,OAAO;;oBAGP,2BAAC,UAAI;wBACH,MAAK;wBACL,OAAO;4BACL,YAAY;4BACZ,cAAc;wBAChB;kCAEA,2BAAC,UAAI;4BAAC,SAAQ;4BAAgB,OAAM;;gCAClC,2BAAC;oCAAK,MAAM;8CAAC;;;;;;gCACb,2BAAC,SAAG;oCAAC,OAAM;oCAAQ,OAAO;wCAAE,YAAY;wCAAG,UAAU;oCAAG;+CAEpD,0BAAA,kBAAkB,IAAI,CACpB,CAAC,IAAM,EAAE,EAAE,KAAK,oBAAoB,MAAM,eAD5C,8CAAA,wBAEG,IAAI;;;;;;gCAGX,2BAAC;oCAAK,MAAK;;wCAAY;wCACd,oBAAoB,OAAO;;;;;;;;;;;;;;;;;;oBAKxC,2BAAC,SAAG;wBAAC,QAAQ;kCACV,kBAAkB,GAAG,CAAC,CAAC,OACtB,2BAAC,SAAG;gCAAC,MAAM;0CAET,2BAAC,UAAI;oCACH,OAAO;wCACL,QAAQ;wCACR,QAAQ,CAAC,UAAU,EACjB,KAAK,EAAE,KAAK,oBAAoB,MAAM,GAClC,YACA,UACL,CAAC;wCACF,UAAU;oCACZ;oCACA,QAAQ;wCAAE,MAAM;4CAAE,SAAS;wCAAG;oCAAE;;wCAE/B,KAAK,EAAE,KAAK,oBAAoB,MAAM,IACrC,2BAAC,SAAG;4CACF,OAAM;4CACN,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,cAAc;gDACd,WAAW;4CACb;sDACD;;;;;;wCAIH,2BAAC;4CACC,OAAO;4CACP,OAAO;gDAAE,WAAW;gDAAU,QAAQ;4CAAa;sDAElD,KAAK,IAAI;;;;;;wCAEZ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;4CAAS,OAAO;gDAAE,cAAc;4CAAG;;gDACrD,KAAK,KAAK,GAAG,IACZ;;wDACE,2BAAC;4DAAM,OAAO;4DAAG,OAAO;gEAAE,cAAc;4DAAE;;gEAAG;gEACzC,KAAK,KAAK;;;;;;;wDAEd,2BAAC;4DAAK,MAAK;sEAAY;;;;;;;mEAGzB,2BAAC;oDACC,OAAO;oDACP,OAAO;wDAAE,OAAO;wDAAW,cAAc;oDAAE;8DAC5C;;;;;;gDAIH,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,WAAW;oDAAE;8DAC1C,KAAK,WAAW;;;;;;;;;;;;wCAOrB,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;4CAAG,OAAO;gDAAE,WAAW;gDAAK,OAAO;4CAAO;sDACzE,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,QAC3B,2BAAC,WAAK;oDAAa,OAAM;;wDACvB,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OAAO;gEACP,WAAW;4DACb;;;;;;wDAEF,2BAAC;sEAAM;;;;;;;mDAPG;;;;;;;;;;wCAYf,KAAK,EAAE,KAAK,oBAAoB,MAAM,GACrC,2BAAC,YAAM;4CACL,MAAK;4CACL,KAAK;4CACL,OAAO;gDACL,WAAW;gDACX,WAAW;4CACb;4CACA,SAAS;gDACP,QAAQ,GAAG,CAAC,SAAS;gDACrB,4BAA4B;4CAC9B;sDACD;;;;;mDAID,2BAAC,YAAM;4CACL,KAAK;4CACL,OAAO;gDACL,WAAW;gDACX,YAAY;gDACZ,aAAa;gDACb,OAAO;4CACT;4CACA,QAAQ;sDACT;;;;;;;;;;;;+BAjGY,KAAK,EAAE;;;;;;;;;;oBA0G9B,2BAAC,UAAI;wBAAC,SAAQ;wBAAS,OAAO;4BAAE,WAAW;wBAAG;kCAC5C,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;YAK3B,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,sBAAsB;gBACtC,QAAQ;oBACN,2BAAC,YAAM;wBAAc,SAAS,IAAM,sBAAsB;kCAAQ;uBAAtD;;;;;oBAGZ,2BAAC,YAAM;wBAEL,MAAK;wBACL,MAAM;wBACN,SAAS;wBACT,SAAS;kCACV;uBALK;;;;;iBAQP;gBACD,OAAO;0BAGP,2BAAC,WAAK;oBAAC,WAAU;oBAAW,OAAM;oBAAS,OAAO;wBAAE,OAAO;wBAAQ,SAAS;oBAAS;;wBACnF,2BAAC,qBAAc;4BAAC,OAAO;gCAAE,UAAU;gCAAI,OAAO;4BAAU;;;;;;wBACxD,2BAAC;4BAAK,MAAM;4BAAC,OAAO;gCAAE,UAAU;4BAAG;sCAAG;;;;;;wBACtC,2BAAC;4BAAK,MAAK;4BAAY,OAAO;gCAAE,WAAW;4BAAS;sCAAG;;;;;;;;;;;;;;;;;;;AAOjE;GArzBM;;QAgFsB,UAAI,CAAC;QAEH,aAAQ;;;KAlFhC;IAuzBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCtzBf;;;eAAA;;;;;;;yDAzCkB;6BACqB;kEAEZ;gEACF;mEACG;;;;;;;;;AAE5B,MAAM,2BAAqC;IACzC,OACE,2BAAC;QAAI,OAAO;YAAE,WAAW;YAAS,YAAY;YAAW,SAAS;QAAO;kBAEvE,2BAAC,UAAI;YACH,OAAO;gBACL,OAAO;gBACP,WAAW;gBACX,cAAc;gBACd,WAAW;YACb;YACA,QAAQ;gBAAE,MAAM;oBAAE,SAAS;gBAAO;YAAE;sBAEpC,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;;oBAEnB,2BAAC,SAAG;wBAAC,IAAI;kCACP,2BAAC,wBAAe;;;;;;;;;;oBAIlB,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,2BAAC,uBAAc;;;;;;;;;;oBAIjB,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;kCACf,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAhCM;IAkCN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCf;;CAEC;;;;4BAUY;;;eAAA;;;;;gCARc;;;;;;;;;AAQpB,MAAM;IACX;;GAEC,GACD,aAAa,eAAwC;QACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,OAA0B,EAAyB;QACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAE,OAA0B,EAAyB;QACrF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAe,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;IACxC;IAEA;;GAEC,GACD,aAAa,eAA2C;QACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;AACF"}